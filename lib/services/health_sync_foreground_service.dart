import 'dart:io';
import 'package:flutter/services.dart';
import 'package:SAiWELL/services/health_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';

class HealthSyncForegroundService {
  static const MethodChannel _channel = MethodChannel('health_sync_service');
  static const MethodChannel _nativeChannel = MethodChannel('health_sync_native');
  
  static bool _isInitialized = false;

  /// Initialize the foreground service communication
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Set up method call handler for Flutter side
    _channel.setMethodCallHandler(_handleMethodCall);
    _isInitialized = true;
  }

  /// Handle method calls from the native foreground service
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'syncHealthData':
        final String uid = call.arguments['uid'] ?? '';
        if (uid.isNotEmpty) {
          await _performHealthSync(uid);
        }
        return true;
      default:
        throw PlatformException(
          code: 'UNIMPLEMENTED',
          message: 'Method ${call.method} not implemented',
        );
    }
  }

  /// Perform the actual health data sync
  static Future<void> _performHealthSync(String uid) async {
    try {
      print('[HealthSyncForegroundService] Starting health data sync for UID: $uid');
      
      final healthService = HealthService();
      
      // Perform health data sync with foreground service context
      await healthService.fetchHealthDataWithDevicesAndSources(
        uid, 
        isBackgroundExecution: false, // This is now running in foreground service context
      );
      
      print('[HealthSyncForegroundService] Health data sync completed successfully');
    } catch (e, stackTrace) {
      print('[HealthSyncForegroundService] Error during health sync: $e');
      print('[HealthSyncForegroundService] Stack trace: $stackTrace');
    }
  }

  /// Start the foreground service for health data sync
  static Future<bool> startHealthSync() async {
    if (!Platform.isAndroid) {
      print('[HealthSyncForegroundService] Foreground service only available on Android');
      return false;
    }

    try {
      // Get the current user UID
      final prefsService = PrefsService();
      final uid = await prefsService.getUid();
      
      if (uid.isEmpty) {
        print('[HealthSyncForegroundService] No UID found, cannot start health sync');
        return false;
      }

      // Initialize if not already done
      await initialize();

      // Start the native foreground service
      final result = await _nativeChannel.invokeMethod('startHealthSync', {
        'uid': uid,
      });

      print('[HealthSyncForegroundService] Foreground service start result: $result');
      return result == true;
    } catch (e) {
      print('[HealthSyncForegroundService] Error starting foreground service: $e');
      return false;
    }
  }

  /// Stop the foreground service
  static Future<bool> stopHealthSync() async {
    if (!Platform.isAndroid) {
      return false;
    }

    try {
      final result = await _nativeChannel.invokeMethod('stopHealthSync');
      print('[HealthSyncForegroundService] Foreground service stop result: $result');
      return result == true;
    } catch (e) {
      print('[HealthSyncForegroundService] Error stopping foreground service: $e');
      return false;
    }
  }

  /// Check if the foreground service is currently running
  static Future<bool> isServiceRunning() async {
    if (!Platform.isAndroid) {
      return false;
    }

    try {
      final result = await _nativeChannel.invokeMethod('isServiceRunning');
      return result == true;
    } catch (e) {
      print('[HealthSyncForegroundService] Error checking service status: $e');
      return false;
    }
  }

  /// Schedule a one-time health sync using foreground service
  static Future<void> scheduleOneTimeSync() async {
    try {
      print('[HealthSyncForegroundService] Scheduling one-time health sync');
      
      // Start the foreground service
      final started = await startHealthSync();
      
      if (started) {
        print('[HealthSyncForegroundService] Foreground service started successfully');
      } else {
        print('[HealthSyncForegroundService] Failed to start foreground service');
      }
    } catch (e) {
      print('[HealthSyncForegroundService] Error scheduling health sync: $e');
    }
  }
}
