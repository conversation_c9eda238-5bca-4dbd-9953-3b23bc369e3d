import 'package:SAiWELL/constants/constant.dart';
import 'package:SAiWELL/models/hrv_reading_model_v2.dart';
import 'package:SAiWELL/models/oxygen_reading_model_v2.dart';
import 'package:SAiWELL/models/sleep_reading_model_v2.dart';
import 'package:SAiWELL/models/steps_reading_model_v2.dart';
import 'package:SAiWELL/services/firebase_remote_config_service.dart';
import 'package:SAiWELL/services/firestore_service.dart';
import 'package:SAiWELL/services/native_communicator.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';
import 'dart:io' show Platform;

import '../utils/dialogs/open_google_health_dialog.dart';
import 'package:SAiWELL/utils/permission_handler_util.dart';

class HealthService {
  late List<HealthDataType> types;
  late List<HealthDataAccess> typesPermission;
  String appPackageName = 'com.google.android.apps.healthdata';
  FirebaseFirestore firestore = FirebaseFirestore.instanceFor(
    app: Firebase.app(),
    databaseId: fbDatabaseId,
  );

  FirestoreService firestoreService = FirestoreService();
  final firebaseRemoteConfigService = FirebaseRemoteConfigService();
  PrefsService prefsService = PrefsService();

  // Flag to prevent repeated permission requests
  bool _isRequestingPermissions = false;
  DateTime? _lastPermissionRequest;

  // Method to reset permission request state
  void resetPermissionRequestState() {
    _isRequestingPermissions = false;
    _lastPermissionRequest = null;
  }

  List<HealthDataType> dataTypeKeysAndroidLegacy = [
    HealthDataType.HEART_RATE,
    HealthDataType.HEIGHT,
    HealthDataType.STEPS,
    HealthDataType.ACTIVE_ENERGY_BURNED,
    HealthDataType.BLOOD_PRESSURE_DIASTOLIC,
    HealthDataType.BLOOD_PRESSURE_SYSTOLIC,
    HealthDataType.BODY_FAT_PERCENTAGE,
    HealthDataType.BODY_MASS_INDEX,
    HealthDataType.BODY_TEMPERATURE,
    HealthDataType.WEIGHT,
    HealthDataType.SLEEP_ASLEEP,
    HealthDataType.SLEEP_AWAKE_IN_BED,
    HealthDataType.SLEEP_AWAKE,
    HealthDataType.SLEEP_DEEP,
    HealthDataType.SLEEP_LIGHT,
    HealthDataType.SLEEP_OUT_OF_BED,
    HealthDataType.SLEEP_REM,
    HealthDataType.SLEEP_SESSION,
    HealthDataType.TOTAL_CALORIES_BURNED,
    HealthDataType.FLIGHTS_CLIMBED,
    HealthDataType.BLOOD_OXYGEN,
    HealthDataType.RESPIRATORY_RATE,
  ];

  HealthService() {
    types = Platform.isAndroid
        ? [] // We'll get actual types from Firebase config
        : dataTypeKeysIOS
            .toList(); // dataTypeKeysIOS is defined in constants.dart

    List<dynamic> typesInString =
        firebaseRemoteConfigService.getHealthDataTypes();

    List<HealthDataType> typesRequired = [];

    for (String type in typesInString) {
      HealthDataType healthDataType = HealthDataType.values.firstWhere(
          (e) => e.toString().split('.').last == type,
          orElse: () => HealthDataType.ELECTROCARDIOGRAM);

      if (healthDataType != HealthDataType.ELECTROCARDIOGRAM) {
        typesRequired.add(healthDataType);
      }
    }
    types = typesRequired;
    types.remove(HealthDataType.ELECTROCARDIOGRAM);
    types.remove(HealthDataType.HIGH_HEART_RATE_EVENT);
    types.remove(HealthDataType.LOW_HEART_RATE_EVENT);
    types.remove(HealthDataType.IRREGULAR_HEART_RATE_EVENT);
    types.remove(HealthDataType.WALKING_HEART_RATE);
    types.remove(HealthDataType.ATRIAL_FIBRILLATION_BURDEN);
    types.remove(HealthDataType.EXERCISE_TIME);

    typesPermission = List.filled(types.length, HealthDataAccess.READ_WRITE);
  }

  Future<void> removeHealthTypesForLowerVersion() async {
    bool isAndroid = Platform.isAndroid;

    if (isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final androidVersion = androidInfo.version.sdkInt;

      if (androidVersion < 34) {
        // For Android versions below 14, use legacy data types
        types.clear();
        types.addAll(dataTypeKeysAndroidLegacy);
        typesPermission = List.filled(types.length, HealthDataAccess.READ_WRITE);
      } else {
        // Android 14+ specific handling
        
        // Universal approach - identify and handle problematic health data types
        // that cause issues across different Android manufacturers
        handleProblematicHealthDataTypes();
        
        // Handle workout related permissions which require companion types on all devices
        if (types.contains(HealthDataType.WORKOUT)) {
          ensureWorkoutCompanionTypes();
        }
        
        // Update permissions list to match the new types list
        typesPermission = List.filled(types.length, HealthDataAccess.READ_WRITE);
      }
    }
  }

  // Handle problematic health data types that cause issues across different devices
  void handleProblematicHealthDataTypes() {
    // Known problematic health data types that often cause permission issues
    List<HealthDataType> knownProblematicTypes = [
      HealthDataType.BASAL_ENERGY_BURNED, // Known to cause repeated dialog issues
    ];
    
    // Remove problematic types to prevent permission dialog loops
    for (var problemType in knownProblematicTypes) {
      if (types.contains(problemType)) {
        types.remove(problemType);
      }
    }
  }

  // Ensure workout has all required companion types for proper functionality
  void ensureWorkoutCompanionTypes() {
    // Required companion types for workout data
    Map<HealthDataType, List<HealthDataType>> requiredCompanionTypes = {
      HealthDataType.WORKOUT: [
        HealthDataType.DISTANCE_DELTA,
        HealthDataType.TOTAL_CALORIES_BURNED,
      ]
    };
    
    // For each main type, ensure all its companion types are included
    for (var entry in requiredCompanionTypes.entries) {
      if (types.contains(entry.key)) {
        for (var companionType in entry.value) {
          if (!types.contains(companionType)) {
            types.add(companionType);
          }
        }
      }
    }
  }

  Future<bool> checkPermission({bool isBackgroundExecution = false}) async {
    bool isIos = Platform.isIOS;

    // For Android background execution, check Health Connect background permissions first
    if (!isIos && isBackgroundExecution) {
      try {
        // Check if background health data access is available on this device
        bool isBackgroundAvailable = await Health().isHealthDataInBackgroundAvailable();
        if (!isBackgroundAvailable) {
          print('[BackgroundFetch] Health Connect background access not available on this device');
          return false;
        }

        // Check if background health data access is authorized
        bool isBackgroundAuthorized = await Health().isHealthDataInBackgroundAuthorized();
        if (!isBackgroundAuthorized) {
          print('[BackgroundFetch] Health Connect background access not authorized');
          return false;
        }

        print('[BackgroundFetch] Health Connect background permissions verified');
      } catch (e) {
        print('[BackgroundFetch] Error checking Health Connect background permissions: $e');
        return false;
      }
    }

    // Prevent repeated permission requests within a short time frame
    if (_isRequestingPermissions) {
      return false;
    }

    // If we just requested permissions recently (within 30 seconds), skip
    if (_lastPermissionRequest != null &&
        DateTime.now().difference(_lastPermissionRequest!).inSeconds < 30) {
      return false;
    }

    // Health permissions are handled differently compared to regular permissions
    // as they use Health package's own authorization system, but we can still use
    // PermissionHandlerUtil for showing dialogs if needed

    if (isIos) {
      NativeCommunicator nativeCommunicator = NativeCommunicator();

      bool hasTypePermissions =
          await Health().hasPermissions(types, permissions: typesPermission) ??
              false;

      if (!hasTypePermissions) {
        // In background execution, don't request new permissions, just return false
        if (isBackgroundExecution) {
          print('[BackgroundFetch] Health permissions not granted, skipping permission request in background');
          return false;
        }

        final granted = await Health()
            .requestAuthorization(types, permissions: typesPermission);
        Future.delayed(const Duration(seconds: 3), () async {
          await nativeCommunicator.requestHealthKitPermissions();
        });
        return granted;
      }

      return true;
    } else {
      // Android code using Health Connect
      await removeHealthTypesForLowerVersion();
      final androidVersion =
          (await DeviceInfoPlugin().androidInfo).version.sdkInt;

      // First configure Health for the appropriate environment
      await Health().configure();

      // For all Android versions, first check if Health Connect is installed
      if (!await isHealthConnectInstalled()) {
        // In background execution, don't show dialogs, just log and return false
        if (isBackgroundExecution) {
          print('[BackgroundFetch] Health Connect not installed, skipping dialog in background');
          return false;
        }

        // Ensure this runs on the UI thread
        Future.microtask(() {
          openGoogleHealthDialog(
              isAppDownloaded: false,
              onTap: () async {
                Get.back();
                await LaunchApp.openApp(
                    androidPackageName: appPackageName, openStore: true);
              });
        });
        return false;
      }

      // Check for permissions and request them if needed
      return await _requestAndroidHealthPermissions(androidVersion, isBackgroundExecution);
    }
  }

  // Extracted method to handle Android health permissions
  Future<bool> _requestAndroidHealthPermissions(int androidVersion, bool isBackgroundExecution) async {
    // Set the flag to prevent repeated requests
    _isRequestingPermissions = true;
    _lastPermissionRequest = DateTime.now();

    try {
      // Check if we already have permissions
      bool hasTypePermissions =
          await Health().hasPermissions(types, permissions: typesPermission) ??
              false;

      if (hasTypePermissions) {
        resetPermissionRequestState();
        return true;
      }

      // In background execution, don't request new permissions, just return false
      // Background execution can only work with already granted permissions
      if (isBackgroundExecution) {
        _isRequestingPermissions = false;
        print('[BackgroundFetch] Health permissions not granted, cannot request permissions in background');
        return false;
      }

      bool authorized = await Health()
          .requestAuthorization(types, permissions: typesPermission);

      // After Health Connect permission dialog is closed, request background authorization
      if (authorized) {
        resetPermissionRequestState();
        // Wait 2 seconds for dialog to close, then request background authorization
        await Future.delayed(const Duration(seconds: 2));
        await Health().requestHealthDataInBackgroundAuthorization();
      } else {
        _isRequestingPermissions = false;
      }

      // If authorization failed, guide user to Health Connect app
      if (!authorized) {
        // Ensure this runs on the UI thread
        Future.microtask(() {
          openGoogleHealthDialog(
              isAppDownloaded: true,
              onTap: () async {
                Get.back();
                await handlePermissionDenied();
              });
        });
      }

      return authorized;
    } catch (e) {
      // Reset flag on error
      _isRequestingPermissions = false;

      // In background execution, don't show dialogs, just log and return false
      if (isBackgroundExecution) {
        print('[BackgroundFetch] Error checking health permissions in background: $e');
        return false;
      }

      // Ensure this runs on the UI thread
      Future.microtask(() {
        openGoogleHealthDialog(
            isAppDownloaded: true,
            onTap: () async {
              Get.back();
              await handlePermissionDenied();
            });
      });
      return false;
    }
  }

  Future<bool> isHealthConnectInstalled() async {
    try {
      final bool isInstalled =
          await LaunchApp.isAppInstalled(androidPackageName: appPackageName);
      return isInstalled;
    } catch (e) {
      // Return false on error to be safe
      return false;
    }
  }

  Future<void> handlePermissionDenied() async {
    if (!Platform.isAndroid) return;

    try {
      final bool isInstalled = await isHealthConnectInstalled();

      if (isInstalled) {
        // For all Android versions, just open the app
        // The deep link parameter doesn't actually do anything different
        await LaunchApp.openApp(
          androidPackageName: appPackageName,
          openStore: false,
        );
      } else {
        // If Health Connect isn't installed, direct to Play Store
        await LaunchApp.openApp(
          androidPackageName: appPackageName,
          openStore: true,
        );
      }
    } catch (e) {
      throw PlatformException(
        code: 'URL_LAUNCH_ERROR',
        message: 'Could not launch Health Connect or Play Store: $e',
      );
    }
  }

  Future<void> fetchHealthDataWithDevicesAndSources(String uid, {bool isBackgroundExecution = false}) async {
    bool isPermissionGranted = await checkPermission(isBackgroundExecution: isBackgroundExecution);
    if (isPermissionGranted) {

      // Fetch existing data first
      DocumentSnapshot existingDoc = await firestore
          .collection('users')
          .doc(uid)
          .collection('health_data')
          .doc('devices_and_sources')
          .get();

      Map<String, dynamic> structuredData = {
        'devices': existingDoc.exists
            ? (existingDoc.data() as Map<String, dynamic>)['devices'] ?? []
            : [],
        'sources': existingDoc.exists
            ? (existingDoc.data() as Map<String, dynamic>)['sources'] ?? {}
            : {},
      };

      var now = DateTime.now();
      List<HealthDataPoint> healthData = await Health().getHealthDataFromTypes(
        types: types,
        startTime: now.subtract(const Duration(days: 1)),
        endTime: now,
      );

      WriteBatch batch = firestore.batch();
      Set<String> devicesSet = Set<String>.from(structuredData['devices']);
      Map<String, Set<String>> deviceSourcesMap = {};
      Set<String> dataTypes = {};

      // Process all health data points
      for (HealthDataPoint dataPoint in healthData) {
        bool isIOS = Platform.isIOS;
        String effectiveSourceId =
            isIOS ? dataPoint.sourceId : dataPoint.sourceName;

        // Skip data that comes from SAiWELL app or has source ID com.saigeware.sh
        if (dataPoint.sourceName != "SAiWELL" &&
            effectiveSourceId != "com.saigeware.sh" &&
            effectiveSourceId != "com.saiwell.sw") {
          // Update sources and devices
          _updateSourcesAndDevices(
              dataPoint, structuredData, devicesSet, deviceSourcesMap);
          // Store individual health data point
          var dataToStore = _createHealthDataPoint(dataPoint);
          bool isAdded =
              await firestoreService.storeHealthDataWithDevicesAndSources(
            uid,
            dataPoint.type.name,
            dataToStore,
            batch,
            false,
          );
          if (isAdded) {
            dataTypes.add(dataPoint.type.name);
          }
        }
      }

      // Update final structured data
      _finalizeStructuredData(structuredData, devicesSet, deviceSourcesMap);

      // Add data types to structured data
      structuredData['dataTypes'] = dataTypes.toList();

      await firestoreService.storeHealthDataWithDevicesAndSourcesInfo(
        uid,
        structuredData,
        batch,
        true,
        dataTypes,
      );

      await batch.commit();
    }
  }

  void _updateSourcesAndDevices(
    HealthDataPoint dataPoint,
    Map<String, dynamic> structuredData,
    Set<String> devicesSet,
    Map<String, Set<String>> deviceSourcesMap,
  ) {
    bool isIOS = Platform.isIOS;
    // Use sourceId for iOS and sourceName for Android
    String effectiveSourceId =
        isIOS ? dataPoint.sourceId : dataPoint.sourceName;

    if (effectiveSourceId.isNotEmpty) {
      structuredData['sources'][effectiveSourceId] = dataPoint.sourceName;
    }

    if (dataPoint.sourceDeviceId.isNotEmpty) {
      devicesSet.add(dataPoint.sourceDeviceId);
      deviceSourcesMap.putIfAbsent(dataPoint.sourceDeviceId, () => {});
      if (effectiveSourceId.isNotEmpty) {
        deviceSourcesMap[dataPoint.sourceDeviceId]!.add(effectiveSourceId);
      }
    }
  }

  Map<String, dynamic> _createHealthDataPoint(
    HealthDataPoint dataPoint,
  ) {
    bool isIOS = Platform.isIOS;
    String effectiveSourceId =
        isIOS ? dataPoint.sourceId : dataPoint.sourceName;

    return {
      'unit': dataPoint.unit.name,
      'startTimestamp': (dataPoint.dateFrom.millisecondsSinceEpoch ~/ 1000),
      'endTimestamp': (dataPoint.dateTo.millisecondsSinceEpoch ~/ 1000),
      'value': dataPoint.value.toJson()['numericValue'],
      'sourceId': effectiveSourceId,
      'uuid': dataPoint.uuid,
      'vitalCollectedTimestamp': Timestamp.fromMillisecondsSinceEpoch(
          dataPoint.dateTo.millisecondsSinceEpoch),
    };
  }

  void _finalizeStructuredData(
    Map<String, dynamic> structuredData,
    Set<String> devicesSet,
    Map<String, Set<String>> deviceSourcesMap,
  ) {
    structuredData['devices'] = devicesSet.toList();
    deviceSourcesMap.forEach((deviceId, sourcesSet) {
      structuredData[deviceId] = {
        'sources': sourcesSet.toList(),
      };
    });
  }

  fetchCombinedHealthData1(String uid, WriteBatch batch,
      Timestamp fromTimestamp, Timestamp now) async {
    debugPrint("------- [fetchCombinedHealthData1] called -------------");

    var toTimeStamp = Timestamp.fromDate(
        fromTimestamp.toDate().add(const Duration(hours: 1)));
    if (toTimeStamp.compareTo(now) < 0) {
      List<HealthDataPoint> healthData = await Health().getHealthDataFromTypes(
          types: types,
          startTime: fromTimestamp.toDate(),
          endTime: toTimeStamp.toDate());

      Map<String, dynamic> combinedIPhoneData = {};
      Map<String, dynamic> combinedWatchData = {};
      Map<String, dynamic> combinedOtherData = {};

      for (HealthDataPoint dataPoint in healthData) {
        bool isIOS = Platform.isIOS;
        String effectiveSourceId =
            isIOS ? dataPoint.sourceId : dataPoint.sourceName;

        // Skip data that comes from SAiWELL app or has source ID com.saigeware.sh
        if (dataPoint.sourceName != "SAiWELL" &&
            effectiveSourceId != "com.saigeware.sh" &&
            effectiveSourceId != "com.saiwell.sw") {
          if (dataPoint.sourceName.contains("Phone")) {
            _updatHealthCombinedMap(
                combinedIPhoneData, dataPoint, fromTimestamp, toTimeStamp);
          } else if (dataPoint.sourceName.contains("Watch")) {
            _updatHealthCombinedMap(
                combinedWatchData, dataPoint, fromTimestamp, toTimeStamp);
          } else {
            _updatHealthCombinedMap(
                combinedOtherData, dataPoint, fromTimestamp, toTimeStamp);
          }
        } else {
          debugPrint(
              "Filtering out combined data - Type: ${dataPoint.type.name}, Source Name: ${dataPoint.sourceName}, Source ID: $effectiveSourceId");
        }
      }

      if (combinedIPhoneData.isNotEmpty) {
        firestoreService.storeCombinedHealthData1(
            uid,
            'Phone',
            combinedIPhoneData,
            batch,
            toTimeStamp.toDate().millisecondsSinceEpoch ~/ 1000);
      }
      if (combinedWatchData.isNotEmpty) {
        firestoreService.storeCombinedHealthData1(
            uid,
            'Watch',
            combinedWatchData,
            batch,
            toTimeStamp.toDate().millisecondsSinceEpoch ~/ 1000);
      }
      if (combinedOtherData.isNotEmpty) {
        firestoreService.storeCombinedHealthData1(
            uid,
            'Other',
            combinedOtherData,
            batch,
            toTimeStamp.toDate().millisecondsSinceEpoch ~/ 1000);
      }

      await firestoreService.updateLastSyncedTimestampForCombinedHealthData1(
          uid, toTimeStamp);
      await fetchCombinedHealthData1(uid, batch, toTimeStamp, now);
    }
  }

  _updatHealthCombinedMap(
      Map<String, dynamic> combinedData,
      HealthDataPoint dataPoint,
      Timestamp fromTimestamp,
      Timestamp toTimeStamp) {
    if (combinedData.containsKey(dataPoint.type.name)) {
      combinedData[dataPoint.type.name] = {
        'unit': dataPoint.unit.name,
        'startTimestamp': fromTimestamp,
        'endTimestamp': toTimeStamp,
        'value': combinedData[dataPoint.type.name]['value'] +
            dataPoint.value.toJson()['numericValue'],
      };
    } else {
      combinedData[dataPoint.type.name] = {
        'unit': dataPoint.unit.name,
        'startTimestamp': fromTimestamp,
        'endTimestamp': toTimeStamp,
        'value': dataPoint.value.toJson()['numericValue'],
      };
    }
  }

  // Add batching capabilities to process health data in chunks
  Future<void> storeHealthDataInBatches<T>(
      List<T> models, Future<bool> Function(T model) processFunction,
      {int batchSize = 50}) async {
    if (models.isEmpty) return;

    for (int i = 0; i < models.length; i += batchSize) {
      final end =
          (i + batchSize < models.length) ? i + batchSize : models.length;
      final batch = models.sublist(i, end);

      await Future.wait(
        batch
            .map((model) => processFunction(model).catchError((e) {
                  return false;
                }))
            .toList(),
      );

      // Add a small delay between batches to prevent overwhelming the system
      await Future.delayed(const Duration(milliseconds: 200));
    }
  }

  storeRingStepDataV2(List<StepsReadingModelV2> models) async {
    await storeHealthDataInBatches<StepsReadingModelV2>(
      models,
      (model) async {
        bool success = true;

        if (model.step > 0) {
          success &= await safeWriteHealthData(
            value: (model.step).toDouble(),
            type: HealthDataType.STEPS,
            startTime: model.date,
          );
        }

        if (model.calories > 0) {
          success &= await safeWriteHealthData(
            unit: HealthDataUnit.KILOCALORIE,
            value: (model.calories).toDouble(),
            type: HealthDataType.ACTIVE_ENERGY_BURNED,
            startTime: model.date,
          );
        }

        if (model.distance > 0) {
          success &= await safeWriteHealthData(
            unit: HealthDataUnit.METER,
            value: (model.distance * 1000).toDouble(),
            type: HealthDataType.DISTANCE_WALKING_RUNNING,
            startTime: model.date,
          );
        }

        return success;
      },
    );
  }

  storeOxygenDataV2(List<BloodOxygenReadingModelV2> models) async {
    await storeHealthDataInBatches<BloodOxygenReadingModelV2>(
      models,
      (model) async {
        if (model.automaticSpo2Data > 0) {
          return await safeWriteHealthData(
            value: (model.automaticSpo2Data / 100).toDouble(),
            type: HealthDataType.BLOOD_OXYGEN,
            startTime: model.date,
          );
        }
        return true;
      },
    );
  }

  storeHrvDataV2(List<HRVReadingModelV2> models) async {
    await storeHealthDataInBatches<HRVReadingModelV2>(
      models,
      (model) async {
        if (model.heartRate > 0) {
          return await safeWriteHealthData(
            value: (model.heartRate).toDouble(),
            type: HealthDataType.HEART_RATE,
            startTime: model.date,
          );
        }
        return true;
      },
    );
  }

  storeSleepDataV2(List<SleepReadingModelV2> models) async {
    await storeHealthDataInBatches<SleepReadingModelV2>(
      models,
      (model) async {
        try {
          bool success = true;

          if (model.remSleepCount > 0) {
            success &= await safeWriteHealthData(
              value: (model.remSleepCount).toDouble(),
              type: HealthDataType.SLEEP_REM,
              startTime: model.startTime,
              endTime: model.endTime,
            );
          }

          if (model.lightSleepCount > 0) {
            // Try with SLEEP_LIGHT first
            bool lightSuccess = await safeWriteHealthData(
              value: (model.lightSleepCount).toDouble(),
              type: HealthDataType.SLEEP_LIGHT,
              startTime: model.startTime,
              endTime: model.endTime,
            );

            if (!lightSuccess) {
              // If that fails, try with SLEEP_ASLEEP as fallback
              success &= await safeWriteHealthData(
                value: (model.lightSleepCount).toDouble(),
                type: HealthDataType.SLEEP_ASLEEP,
                startTime: model.startTime,
                endTime: model.endTime,
              );
            } else {
              success &= true;
            }
          }

          if (model.awakeCount > 0) {
            success &= await safeWriteHealthData(
              value: (model.awakeCount).toDouble(),
              type: HealthDataType.SLEEP_AWAKE,
              startTime: model.startTime,
              endTime: model.endTime,
            );
          }

          if ((model.deepSleepCount + model.otherSleepCount) > 0) {
            success &= await safeWriteHealthData(
              value:
                  ((model.deepSleepCount + model.otherSleepCount)).toDouble(),
              type: HealthDataType.SLEEP_DEEP,
              startTime: model.startTime,
              endTime: model.endTime,
            );
          }

          return success;
        } catch (e) {
          return false;
        }
      },
    );
  }

  Future<bool> safeWriteHealthData({
    required double value,
    required HealthDataType type,
    required DateTime startTime,
    DateTime? endTime,
    HealthDataUnit? unit,
  }) async {
    try {
      return await Health().writeHealthData(
        value: value,
        type: type,
        startTime: startTime,
        endTime: endTime ?? startTime.add(const Duration(minutes: 1)),
        unit: unit,
      );
    } catch (e) {
      return false;
    }
  }
}
