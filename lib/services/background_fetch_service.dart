import 'package:workmanager/workmanager.dart';
import 'package:SAiWELL/services/health_sync_foreground_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:SAiWELL/firebase_options.dart';

const backgroundSyncTask = "backgroundSyncTask";

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case backgroundSyncTask:
        try {
          print('[BackgroundFetch] Starting backgroundSyncTask');
          // Initialize Firebase in background isolate
          try {
            await Firebase.initializeApp(
              options: DefaultFirebaseOptions.currentPlatform,
            );
            print('[BackgroundFetch] Firebase initialized');
          } catch (e) {
            print('[BackgroundFetch] Firebase already initialized or error: ' + e.toString());
          }

          // Initialize PrefsService and get UID
          final prefsService = PrefsService();
          String uid = await prefsService.getUid();
          print('[BackgroundFetch] UID from PrefsService: $uid');

          if (uid.isNotEmpty) {
            // Use foreground service for Health Connect access
            print('[BackgroundFetch] Starting foreground service for health sync');
            await HealthSyncForegroundService.startHealthSync();
            print('[BackgroundFetch] Health data sync initiated via foreground service');
          } else {
            print('[BackgroundFetch] UID is empty, skipping health data upload');
          }

          print('[BackgroundFetch] backgroundSyncTask completed');
          return Future.value(true);
        } catch (e, stack) {
          print('[BackgroundFetch] Error: ' + e.toString());
          print(stack);
          return Future.value(false);
        }
    }
    return Future.value(false);
  });
}

class BackgroundFetchService {
  static void initialize() {
    Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: true,
    );
    Workmanager().registerPeriodicTask(
      "1",
      backgroundSyncTask,
      frequency: Duration(minutes: 15),
    );
  }
}
