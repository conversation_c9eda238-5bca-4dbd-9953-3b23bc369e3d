import 'package:workmanager/workmanager.dart';
import 'package:SAiWELL/services/health_service.dart';
import 'package:SAiWELL/services/prefs_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:SAiWELL/firebase_options.dart';
import 'package:flutter/services.dart';
import 'dart:io';

const backgroundSyncTask = "backgroundSyncTask";

@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case backgroundSyncTask:
        try {
          print('[BackgroundFetch] Starting backgroundSyncTask');
          // Initialize Firebase in background isolate
          try {
            await Firebase.initializeApp(
              options: DefaultFirebaseOptions.currentPlatform,
            );
            print('[BackgroundFetch] Firebase initialized');
          } catch (e) {
            print('[BackgroundFetch] Firebase already initialized or error: ' + e.toString());
          }

          // Initialize PrefsService and get UID
          final prefsService = PrefsService();
          String uid = await prefsService.getUid();
          print('[BackgroundFetch] UID from PrefsService: $uid');

          if (uid.isNotEmpty) {
            // Try to start foreground service for Health Connect access
            print('[BackgroundFetch] Attempting to start foreground service');
            try {
              const platform = MethodChannel('health_sync_background');
              await platform.invokeMethod('startHealthSyncFromBackground', {'uid': uid});
              print('[BackgroundFetch] Foreground service started successfully');
            } catch (e) {
              print('[BackgroundFetch] Failed to start foreground service: $e');
              // Fallback to direct health service call (may fail with SecurityException)
              print('[BackgroundFetch] Falling back to direct health service call');
              final healthService = HealthService();
              await healthService.fetchHealthDataWithDevicesAndSources(
                uid,
                isBackgroundExecution: true,
              );
              print('[BackgroundFetch] Direct health data sync completed');
            }
          } else {
            print('[BackgroundFetch] UID is empty, skipping health data upload');
          }

          print('[BackgroundFetch] backgroundSyncTask completed');
          return Future.value(true);
        } catch (e, stack) {
          print('[BackgroundFetch] Error: ' + e.toString());
          print(stack);
          return Future.value(false);
        }
    }
    return Future.value(false);
  });
}

class BackgroundFetchService {
  static void initialize() {
    Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: true,
    );
    Workmanager().registerPeriodicTask(
      "1",
      backgroundSyncTask,
      frequency: Duration(minutes: 15),
    );
  }
}
