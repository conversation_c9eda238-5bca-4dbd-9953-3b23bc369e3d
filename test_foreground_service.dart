import 'package:flutter/material.dart';
import 'package:SAiWELL/services/health_sync_foreground_service.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Foreground Service Test',
      home: ForegroundServiceTestPage(),
    );
  }
}

class ForegroundServiceTestPage extends StatefulWidget {
  @override
  _ForegroundServiceTestPageState createState() => _ForegroundServiceTestPageState();
}

class _ForegroundServiceTestPageState extends State<ForegroundServiceTestPage> {
  String _status = 'Ready to test';
  bool _isServiceRunning = false;

  @override
  void initState() {
    super.initState();
    _checkServiceStatus();
  }

  Future<void> _checkServiceStatus() async {
    try {
      final isRunning = await HealthSyncForegroundService.isServiceRunning();
      setState(() {
        _isServiceRunning = isRunning;
        _status = isRunning ? 'Service is running' : 'Service is stopped';
      });
    } catch (e) {
      setState(() {
        _status = 'Error checking status: $e';
      });
    }
  }

  Future<void> _startService() async {
    setState(() {
      _status = 'Starting service...';
    });

    try {
      final success = await HealthSyncForegroundService.startHealthSync();
      setState(() {
        _status = success ? 'Service started successfully' : 'Failed to start service';
        _isServiceRunning = success;
      });
    } catch (e) {
      setState(() {
        _status = 'Error starting service: $e';
      });
    }
  }

  Future<void> _stopService() async {
    setState(() {
      _status = 'Stopping service...';
    });

    try {
      final success = await HealthSyncForegroundService.stopHealthSync();
      setState(() {
        _status = success ? 'Service stopped successfully' : 'Failed to stop service';
        _isServiceRunning = !success;
      });
    } catch (e) {
      setState(() {
        _status = 'Error stopping service: $e';
      });
    }
  }

  Future<void> _scheduleSync() async {
    setState(() {
      _status = 'Scheduling one-time sync...';
    });

    try {
      await HealthSyncForegroundService.scheduleOneTimeSync();
      setState(() {
        _status = 'One-time sync scheduled';
      });
    } catch (e) {
      setState(() {
        _status = 'Error scheduling sync: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Foreground Service Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Service Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        color: _isServiceRunning ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _startService,
              child: Text('Start Health Sync Service'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _stopService,
              child: Text('Stop Health Sync Service'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _scheduleSync,
              child: Text('Schedule One-Time Sync'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkServiceStatus,
              child: Text('Refresh Status'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
