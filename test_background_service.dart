import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:SAiWELL/services/prefs_service.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Background Service Test',
      home: BackgroundServiceTestPage(),
    );
  }
}

class BackgroundServiceTestPage extends StatefulWidget {
  @override
  _BackgroundServiceTestPageState createState() => _BackgroundServiceTestPageState();
}

class _BackgroundServiceTestPageState extends State<BackgroundServiceTestPage> {
  String _status = 'Ready to test background service';
  String _currentUid = 'Loading...';

  @override
  void initState() {
    super.initState();
    _loadCurrentUid();
  }

  Future<void> _loadCurrentUid() async {
    try {
      final prefsService = PrefsService();
      final uid = await prefsService.getUid();
      setState(() {
        _currentUid = uid.isNotEmpty ? uid : 'No UID found';
      });
    } catch (e) {
      setState(() {
        _currentUid = 'Error loading UID: $e';
      });
    }
  }

  Future<void> _testBackgroundServiceCall() async {
    setState(() {
      _status = 'Testing background service call...';
    });

    try {
      const platform = MethodChannel('health_sync_background');
      final result = await platform.invokeMethod('startHealthSyncFromBackground', {
        'uid': _currentUid,
      });
      
      setState(() {
        _status = 'Background service call successful: $result';
      });
    } catch (e) {
      setState(() {
        _status = 'Background service call failed: $e';
      });
    }
  }

  Future<void> _testForegroundServiceCall() async {
    setState(() {
      _status = 'Testing foreground service call...';
    });

    try {
      const platform = MethodChannel('health_sync_native');
      final result = await platform.invokeMethod('startHealthSync', {
        'uid': _currentUid,
      });
      
      setState(() {
        _status = 'Foreground service call successful: $result';
      });
    } catch (e) {
      setState(() {
        _status = 'Foreground service call failed: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Background Service Test'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      'Service Test Status',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Current UID: $_currentUid',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _testBackgroundServiceCall,
              child: Text('Test Background Service Call'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: _testForegroundServiceCall,
              child: Text('Test Foreground Service Call'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 20),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Instructions:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text('1. Test Background Service Call - simulates WorkManager background task'),
                    Text('2. Test Foreground Service Call - simulates normal app usage'),
                    Text('3. Both should start the foreground service and show notification'),
                    Text('4. Check Android logs for detailed service behavior'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
