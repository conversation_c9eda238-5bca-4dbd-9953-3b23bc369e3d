package com.saiwell.sw

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class HealthSyncService : Service() {
    companion object {
        const val CHANNEL_ID = "health_sync_channel"
        const val NOTIFICATION_ID = 1001
        const val ACTION_START_HEALTH_SYNC = "START_HEALTH_SYNC"
        const val ACTION_STOP_HEALTH_SYNC = "STOP_HEALTH_SYNC"
        
        fun startService(context: Context, uid: String) {
            val intent = Intent(context, HealthSyncService::class.java).apply {
                action = ACTION_START_HEALTH_SYNC
                putExtra("uid", uid)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, HealthSyncService::class.java).apply {
                action = ACTION_STOP_HEALTH_SYNC
            }
            context.stopService(intent)
        }
    }

    private var flutterEngine: FlutterEngine? = null
    private var methodChannel: MethodChannel? = null

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_HEALTH_SYNC -> {
                val uid = intent.getStringExtra("uid") ?: ""
                startForegroundService()
                startHealthSync(uid)
            }
            ACTION_STOP_HEALTH_SYNC -> {
                stopSelf()
            }
        }
        return START_NOT_STICKY
    }

    private fun startForegroundService() {
        val notification = createNotification()
        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Health Data Sync",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Syncing health data in background"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Health Data Sync")
            .setContentText("Syncing health data...")
            .setSmallIcon(R.drawable.launch_background) // Use your app icon
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build()
    }

    private fun startHealthSync(uid: String) {
        try {
            // Initialize Flutter engine for background execution
            flutterEngine = FlutterEngine(this)
            
            // Set up method channel to communicate with Flutter
            methodChannel = MethodChannel(
                flutterEngine!!.dartExecutor.binaryMessenger,
                "health_sync_service"
            )
            
            // Start the Flutter Dart isolate
            flutterEngine!!.dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
            
            // Call Flutter method to sync health data
            methodChannel?.invokeMethod("syncHealthData", mapOf("uid" to uid)) { result ->
                // Health sync completed
                stopSelf()
            }
            
        } catch (e: Exception) {
            // Handle error and stop service
            stopSelf()
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        flutterEngine?.destroy()
        flutterEngine = null
        methodChannel = null
    }
}
