<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Check whether Health Connect is installed or not -->
    <queries>
        <package android:name="com.google.android.apps.healthdata" />
        <intent>
            <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="com.google.android.gms.permission.AD_ID   permission"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" />
    
    <!-- Bluetooth permissions -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    
    <uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
    
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.VIBRATE"/>

    <!-- Notification permissions for Android 13+ (API 33+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!-- Additional permissions for reliable background notifications -->
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/>

    <!-- Permission for exact alarms (Android 12+) - needed for 100% reliable notifications when app is killed -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM"/>



    <!-- Health-related permissions -->
    <!-- Background Health Connect permission for Android 15+ -->
    <uses-permission android:name="android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND"/>

    <uses-permission android:name="android.permission.health.READ_ACTIVE_ENERGY_BURNED"/>
    <uses-permission android:name="android.permission.health.WRITE_ACTIVE_ENERGY_BURNED"/>
    <uses-permission android:name="android.permission.health.READ_BLOOD_OXYGEN"/>
    <uses-permission android:name="android.permission.health.WRITE_BLOOD_OXYGEN"/>
    <uses-permission android:name="android.permission.health.READ_BLOOD_PRESSURE"/>
    <uses-permission android:name="android.permission.health.WRITE_BLOOD_PRESSURE"/>
    <uses-permission android:name="android.permission.health.READ_BODY_FAT_PERCENTAGE"/>
    <uses-permission android:name="android.permission.health.WRITE_BODY_FAT_PERCENTAGE"/>
    <uses-permission android:name="android.permission.health.READ_BODY_MASS_INDEX"/>
    <uses-permission android:name="android.permission.health.WRITE_BODY_MASS_INDEX"/>
    <uses-permission android:name="android.permission.health.READ_BODY_TEMPERATURE"/>
    <uses-permission android:name="android.permission.health.WRITE_BODY_TEMPERATURE"/>
    <uses-permission android:name="android.permission.health.READ_HEART_RATE"/>
    <uses-permission android:name="android.permission.health.WRITE_HEART_RATE"/>
    <uses-permission android:name="android.permission.health.READ_HEIGHT"/>
    <uses-permission android:name="android.permission.health.WRITE_HEIGHT"/>
    <uses-permission android:name="android.permission.health.READ_STEPS"/>
    <uses-permission android:name="android.permission.health.WRITE_STEPS"/>
    <uses-permission android:name="android.permission.health.READ_WEIGHT"/>
    <uses-permission android:name="android.permission.health.WRITE_WEIGHT"/>
    <uses-permission android:name="android.permission.health.READ_SLEEP"/>
    <uses-permission android:name="android.permission.health.WRITE_SLEEP"/>
    <uses-permission android:name="android.permission.health.READ_WORKOUT"/>
    <uses-permission android:name="android.permission.health.WRITE_WORKOUT"/>
    <uses-permission android:name="android.permission.health.READ_RESTING_HEART_RATE"/>
    <uses-permission android:name="android.permission.health.WRITE_RESTING_HEART_RATE"/>
    <uses-permission android:name="android.permission.health.READ_FLIGHTS_CLIMBED"/>
    <uses-permission android:name="android.permission.health.WRITE_FLIGHTS_CLIMBED"/>
    <uses-permission android:name="android.permission.health.READ_BASAL_ENERGY_BURNED"/>
    <uses-permission android:name="android.permission.health.WRITE_BASAL_ENERGY_BURNED"/>
    <uses-permission android:name="android.permission.health.READ_RESPIRATORY_RATE"/>
    <uses-permission android:name="android.permission.health.WRITE_RESPIRATORY_RATE"/>
    <uses-permission android:name="android.permission.health.READ_TOTAL_CALORIES_BURNED"/>
    <uses-permission android:name="android.permission.health.WRITE_TOTAL_CALORIES_BURNED"/>
    <uses-permission android:name="android.permission.health.READ_HEART_RATE_VARIABILITY"/>
    <uses-permission android:name="android.permission.health.WRITE_HEART_RATE_VARIABILITY"/>
    <uses-permission android:name="android.permission.health.READ_ACTIVE_CALORIES_BURNED"/>
    <uses-permission android:name="android.permission.health.WRITE_ACTIVE_CALORIES_BURNED"/>
    <uses-permission android:name="android.permission.health.READ_OXYGEN_SATURATION"/>
    <uses-permission android:name="android.permission.health.WRITE_OXYGEN_SATURATION"/>
    <uses-permission android:name="android.permission.health.READ_BODY_FAT"/>
    <uses-permission android:name="android.permission.health.WRITE_BODY_FAT"/>
    <uses-permission android:name="android.permission.health.READ_EXERCISE"/>
    <uses-permission android:name="android.permission.health.WRITE_EXERCISE"/>
    <uses-permission android:name="android.permission.health.READ_FLOORS_CLIMBED"/>
    <uses-permission android:name="android.permission.health.WRITE_FLOORS_CLIMBED"/>
    <!-- Additional exercise-related permissions needed for WORKOUT types -->
    <uses-permission android:name="android.permission.health.READ_DISTANCE"/>
    <uses-permission android:name="android.permission.health.WRITE_DISTANCE"/>
    <uses-permission android:name="android.permission.health.READ_SPEED"/>
    <uses-permission android:name="android.permission.health.WRITE_SPEED"/>
    <uses-permission android:name="android.permission.health.READ_CALORIES"/>
    <uses-permission android:name="android.permission.health.WRITE_CALORIES"/>


    <queries>
        <package android:name="com.saigeware.sh.saiwell" />
    </queries>

    <application
        android:label="SAiWELL"
        android:name="${applicationName}"
        android:usesCleartextTraffic="true"
        android:icon="@mipmap/launcher_icon"
        tools:replace="android:label">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->

            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
                <category android:name="android.intent.category.HOME"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <!-- Intention to show Permissions screen for Health Connect API -->
            <intent-filter>
                <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
            </intent-filter>
            
            <!-- NFC and URL scheme handling -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="saiwell" />
            </intent-filter>
            
            <!-- Handle NFC tags -->
            <intent-filter>
                <action android:name="android.nfc.action.NDEF_DISCOVERED" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="saiwell" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <activity-alias
            android:name="ViewPermissionUsageActivity"
            android:exported="true"
            android:targetActivity=".MainActivity"
            android:permission="android.permission.START_VIEW_PERMISSION_USAGE">
            <intent-filter>
                <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />
                <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
            </intent-filter>
        </activity-alias>

        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
            
        <!-- BLE Service for Ring SDK -->
        <service
            android:name=".BleService"
            android:enabled="true"
            android:exported="false" />

        <!-- Health Data Sync Foreground Service -->
        <service
            android:name=".HealthSyncService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="health" />

        <!-- Boot receiver to reschedule notifications after device restart -->
        <receiver
            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationBootReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />
                <data android:scheme="package" />
            </intent-filter>
        </receiver>

        <!-- Notification receiver for handling notification actions -->
        <receiver
            android:name="com.dexterous.flutterlocalnotifications.ScheduledNotificationReceiver"
            android:enabled="true"
            android:exported="false" />
    </application>
</manifest>
