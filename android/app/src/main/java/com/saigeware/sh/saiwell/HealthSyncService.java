package com.saigeware.sh.saiwell;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.dart.DartExecutor;
import io.flutter.plugin.common.MethodChannel;
import java.util.HashMap;
import java.util.Map;

public class HealthSyncService extends Service {
    private static final String TAG = "HealthSyncService";
    public static final String CHANNEL_ID = "health_sync_channel";
    public static final int NOTIFICATION_ID = 1001;
    public static final String ACTION_START_HEALTH_SYNC = "START_HEALTH_SYNC";
    public static final String ACTION_STOP_HEALTH_SYNC = "STOP_HEALTH_SYNC";
    
    private FlutterEngine flutterEngine;
    private MethodChannel methodChannel;

    public static void startService(Context context, String uid) {
        Intent intent = new Intent(context, HealthSyncService.class);
        intent.setAction(ACTION_START_HEALTH_SYNC);
        intent.putExtra("uid", uid);
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent);
        } else {
            context.startService(intent);
        }
    }
    
    public static void stopService(Context context) {
        Intent intent = new Intent(context, HealthSyncService.class);
        intent.setAction(ACTION_STOP_HEALTH_SYNC);
        context.stopService(intent);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "HealthSyncService onCreate");
        createNotificationChannel();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "HealthSyncService onStartCommand");
        
        if (intent != null) {
            String action = intent.getAction();
            if (ACTION_START_HEALTH_SYNC.equals(action)) {
                String uid = intent.getStringExtra("uid");
                if (uid == null) uid = "";
                startForegroundService();
                startHealthSync(uid);
            } else if (ACTION_STOP_HEALTH_SYNC.equals(action)) {
                stopSelf();
            }
        }
        
        return START_NOT_STICKY;
    }

    private void startForegroundService() {
        Log.d(TAG, "Starting foreground service");
        Notification notification = createNotification();
        startForeground(NOTIFICATION_ID, notification);
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "Health Data Sync",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Syncing health data in background");
            channel.setShowBadge(false);
            
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }

    private Notification createNotification() {
        Intent intent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Health Data Sync")
            .setContentText("Syncing health data...")
            .setSmallIcon(R.drawable.launch_background)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .build();
    }

    private void startHealthSync(String uid) {
        Log.d(TAG, "Starting health sync for UID: " + uid);
        
        try {
            // Initialize Flutter engine for background execution
            flutterEngine = new FlutterEngine(this);
            
            // Set up method channel to communicate with Flutter
            methodChannel = new MethodChannel(
                flutterEngine.getDartExecutor().getBinaryMessenger(),
                "health_sync_service"
            );
            
            // Start the Flutter Dart isolate
            flutterEngine.getDartExecutor().executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            );
            
            // Call Flutter method to sync health data
            Map<String, Object> arguments = new HashMap<>();
            arguments.put("uid", uid);
            
            methodChannel.invokeMethod("syncHealthData", arguments, new MethodChannel.Result() {
                @Override
                public void success(Object result) {
                    Log.d(TAG, "Health sync completed successfully");
                    stopSelf();
                }

                @Override
                public void error(String errorCode, String errorMessage, Object errorDetails) {
                    Log.e(TAG, "Health sync failed: " + errorMessage);
                    stopSelf();
                }

                @Override
                public void notImplemented() {
                    Log.e(TAG, "Health sync method not implemented");
                    stopSelf();
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error starting health sync: " + e.getMessage(), e);
            stopSelf();
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "HealthSyncService onDestroy");
        super.onDestroy();
        
        if (flutterEngine != null) {
            flutterEngine.destroy();
            flutterEngine = null;
        }
        methodChannel = null;
    }
}
