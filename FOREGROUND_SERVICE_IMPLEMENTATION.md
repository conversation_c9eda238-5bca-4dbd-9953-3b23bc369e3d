# Health Connect Foreground Service Implementation

## Overview
This document outlines the complete implementation of a foreground service solution to resolve Android Health Connect permission issues when the app is killed. The solution replaces WorkManager background tasks with a proper foreground service that maintains Health Connect permissions.

## Problem Solved
- **Issue**: Android Health Connect permissions are revoked when the app is killed, causing SecurityException errors during background health data fetching
- **Root Cause**: Health Connect prohibits background data reading from WorkManager tasks for security reasons
- **Solution**: Implement a foreground service with "health" type that can maintain Health Connect permissions during background execution

## Architecture

### 1. Native Android Service
**File**: `android/app/src/main/kotlin/com/saiwell/sw/HealthSyncService.kt`
- Foreground service with "health" type for Health Connect compliance
- Notification management for user awareness
- Flutter engine initialization for Dart code execution
- Method channel communication with Flutter side

### 2. Flutter Service Wrapper
**File**: `lib/services/health_sync_foreground_service.dart`
- Flutter wrapper for native foreground service communication
- Static methods for service control: `startHealthSync()`, `stopHealthSync()`, `isServiceRunning()`
- Health data sync orchestration in foreground service context
- Method channel handlers for bidirectional communication

### 3. MainActivity Integration
**File**: `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`
- Method channel handler for `health_sync_native` channel
- Service start/stop/status methods
- Integration with existing BLE and notification channels

### 4. Background Fetch Service Update
**File**: `lib/services/background_fetch_service.dart`
- Modified to use foreground service instead of direct Health Connect access
- Calls `HealthSyncForegroundService.startHealthSync()` for background tasks
- Maintains WorkManager integration for scheduling

## Key Components

### Android Manifest Permissions
```xml
<!-- Foreground service permission -->
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

<!-- Background Health Connect permission for Android 15+ -->
<uses-permission android:name="android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND"/>

<!-- Service declaration -->
<service
    android:name="com.saiwell.sw.HealthSyncService"
    android:enabled="true"
    android:exported="false"
    android:foregroundServiceType="health" />
```

### Method Channel Communication
- **Channel**: `health_sync_native`
- **Methods**:
  - `startHealthSync`: Start foreground service with UID parameter
  - `stopHealthSync`: Stop foreground service
  - `isServiceRunning`: Check service status

### Notification System
- **Channel ID**: `health_sync_channel`
- **Channel Name**: "Health Data Sync"
- **Notification**: Shows during foreground service execution
- **Importance**: Default (non-intrusive but visible)

## Implementation Flow

1. **Background Task Trigger**: WorkManager triggers background task
2. **Service Start**: Background task calls `HealthSyncForegroundService.startHealthSync()`
3. **Native Service**: Flutter calls native Android service via method channel
4. **Foreground Service**: Native service starts as foreground service with notification
5. **Health Sync**: Service initializes Flutter engine and calls health sync method
6. **Data Fetch**: Health Connect data is fetched with maintained permissions
7. **Service Stop**: Service automatically stops after sync completion

## Testing

### Test File
**File**: `test_foreground_service.dart`
- Complete UI for testing foreground service functionality
- Service start/stop controls
- Status monitoring
- One-time sync scheduling

### Test Scenarios
1. **Service Start**: Verify service starts and notification appears
2. **Background Sync**: Kill app and verify background sync works without SecurityException
3. **Permission Persistence**: Confirm Health Connect permissions remain active
4. **Service Stop**: Verify service stops and notification disappears
5. **Error Handling**: Test error scenarios and recovery

## Benefits

1. **Permission Persistence**: Health Connect permissions maintained during background execution
2. **User Awareness**: Foreground notification informs user of background activity
3. **Android Compliance**: Follows Android 14+ foreground service requirements
4. **Reliable Sync**: Eliminates SecurityException errors for health data fetching
5. **Future-Proof**: Compatible with Android 15+ background permission requirements

## Usage

### Start Health Sync
```dart
final success = await HealthSyncForegroundService.startHealthSync();
```

### Stop Health Sync
```dart
final success = await HealthSyncForegroundService.stopHealthSync();
```

### Check Service Status
```dart
final isRunning = await HealthSyncForegroundService.isServiceRunning();
```

### Schedule One-Time Sync
```dart
await HealthSyncForegroundService.scheduleOneTimeSync();
```

## Next Steps

1. **Test Complete Integration**: Verify all components work together
2. **Background Testing**: Test with app killed to confirm SecurityException resolution
3. **Performance Monitoring**: Monitor battery usage and performance impact
4. **User Experience**: Ensure notification system provides good UX
5. **Error Handling**: Implement comprehensive error handling and recovery

## Files Modified/Created

### Created Files
- `android/app/src/main/kotlin/com/saiwell/sw/HealthSyncService.kt`
- `lib/services/health_sync_foreground_service.dart`
- `test_foreground_service.dart`
- `FOREGROUND_SERVICE_IMPLEMENTATION.md`

### Modified Files
- `android/app/src/main/java/com/saigeware/sh/saiwell/MainActivity.java`
- `android/app/src/main/AndroidManifest.xml`
- `lib/services/background_fetch_service.dart`

This implementation provides a robust solution for maintaining Health Connect permissions during background execution while following Android best practices for foreground services.
